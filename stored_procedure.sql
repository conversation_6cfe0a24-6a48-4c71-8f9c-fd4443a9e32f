DELIMITER $$

CREATE PROCEDURE `MoveCollectionData`(IN collectionName VARCHAR(255))
BEGIN
    DECLARE v_sqlstate VARCHAR(5) DEFAULT '00000';
    DECLARE v_message TEXT DEFAULT '';
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            v_sqlstate = RETURNED_SQLSTATE, 
            v_message = MESSAGE_TEXT;
        ROLLBACK;
        SELECT v_sqlstate AS sqlstate, v_message AS message;
    END;

    START TRANSACTION;
    
    -- 插入集合数据
    INSERT INTO aqgl.collections (`key`, name, title, inherit, hidden, options, description, sort)
    SELECT `key`, name, title, inherit, hidden, options, description, sort
    FROM anb.collections 
    WHERE name = collectionName;
    
    -- 插入字段数据
    INSERT INTO aqgl.fields (`key`, name, type, interface, description, collection_name, parent_key, reverse_key, options, sort)
    SELECT `key`, name, type, interface, description, collection_name, parent_key, reverse_key, options, sort
    FROM anb.fields 
    WHERE collection_name = collectionName;
    
    COMMIT;
    
    SELECT 'SUCCESS' AS status, CONCAT('Collection "', collectionName, '" moved successfully') AS message;
END$$

DELIMITER ;



